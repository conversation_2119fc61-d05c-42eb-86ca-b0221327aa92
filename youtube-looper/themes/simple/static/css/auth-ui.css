/* ============================================================================
   AUTHENTICATION UI STYLES
   ============================================================================ */

/* Authentication Container */
.auth-container {
  display: flex;
  align-items: center;
  gap: 12px;
  position: relative;
}

/* Sign In Container */
.auth-sign-in-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Google Sign In Button */
.auth-google-sign-in-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-google-sign-in-btn:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2d8f47 100%);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.auth-google-sign-in-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.auth-google-sign-in-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Email Sign In Button */
.auth-email-sign-in-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.auth-email-sign-in-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-1px);
}

.auth-email-sign-in-btn:active {
  transform: translateY(0);
}

.auth-email-sign-in-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

/* Loading State */
.auth-loading {
  display: flex;
  align-items: center;
  gap: 8px;
}

.auth-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: auth-spin 1s linear infinite;
}

@keyframes auth-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* User Profile Button */
.auth-user-profile-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  color: inherit;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.auth-user-profile-btn:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

/* User Avatar */
.auth-user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.auth-avatar-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

/* User Info */
.auth-user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-width: 0;
}

.auth-user-name {
  font-weight: 500;
  font-size: 14px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

.auth-user-email {
  font-size: 12px;
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

/* Dropdown Arrow */
.auth-dropdown-arrow {
  transition: transform 0.2s ease;
  opacity: 0.7;
}

.auth-user-profile-btn:hover .auth-dropdown-arrow {
  opacity: 1;
}

/* User Dropdown */
.auth-user-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  margin-top: 8px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  min-width: 240px;
  opacity: 0;
  visibility: hidden;
  transform: translateY(-10px);
  transition: all 0.2s ease;
  z-index: 1000;
  color: #333;
}

.auth-user-dropdown.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

/* Dropdown Header */
.auth-dropdown-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
}

.auth-dropdown-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.auth-dropdown-info {
  flex: 1;
  min-width: 0;
}

.auth-dropdown-name {
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.auth-dropdown-email {
  font-size: 14px;
  opacity: 0.7;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Dropdown Divider */
.auth-dropdown-divider {
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
  margin: 0 16px;
}

/* Dropdown Items */
.auth-dropdown-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px 16px;
  background: none;
  border: none;
  color: inherit;
  font-size: 14px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  text-align: left;
}

.auth-dropdown-item:hover {
  background: rgba(0, 0, 0, 0.05);
}

.auth-dropdown-item:first-of-type {
  border-radius: 0 0 12px 12px;
}

/* Authentication Status Indicator */
.auth-status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.auth-status-indicator.authenticated {
  background: #4caf50;
  box-shadow: 0 0 8px rgba(76, 175, 80, 0.4);
}

.auth-status-indicator.not-authenticated {
  background: #ff9800;
  box-shadow: 0 0 8px rgba(255, 152, 0, 0.4);
}

/* Authentication Modal */
.auth-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.auth-modal.show {
  opacity: 1;
  visibility: visible;
}

.auth-modal-content {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  padding: 32px;
  width: 100%;
  max-width: 400px;
  margin: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  color: #333;
  transform: translateY(20px);
  transition: transform 0.3s ease;
}

.auth-modal.show .auth-modal-content {
  transform: translateY(0);
}

.auth-modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.auth-modal-header h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.auth-modal-close {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
  color: #666;
}

.auth-modal-close:hover {
  background: rgba(0, 0, 0, 0.1);
}

/* Authentication Form */
.auth-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.auth-form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.auth-form-group label {
  font-weight: 500;
  color: #555;
  font-size: 14px;
}

.auth-form-group input {
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 8px;
  font-size: 16px;
  transition: border-color 0.2s ease;
  background: rgba(255, 255, 255, 0.8);
}

.auth-form-group input:focus {
  outline: none;
  border-color: #4285f4;
  box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
}

.auth-form-submit {
  padding: 12px 24px;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.auth-form-submit:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2d8f47 100%);
  transform: translateY(-1px);
}

.auth-form-submit:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.auth-form-forgot {
  background: none;
  border: none;
  color: #4285f4;
  cursor: pointer;
  font-size: 14px;
  text-decoration: underline;
  padding: 8px 0;
}

.auth-form-forgot:hover {
  color: #3367d6;
}

.auth-form-footer {
  margin-top: 20px;
  text-align: center;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.auth-form-footer p {
  margin: 0;
  color: #666;
  font-size: 14px;
}

.auth-form-toggle {
  background: none;
  border: none;
  color: #4285f4;
  cursor: pointer;
  font-weight: 500;
  text-decoration: underline;
  font-size: 14px;
}

.auth-form-toggle:hover {
  color: #3367d6;
}

/* Authentication Call-to-Action */
.auth-cta {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.auth-cta-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background: linear-gradient(135deg, #4285f4 0%, #34a853 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  justify-content: center;
}

.auth-cta-btn:hover {
  background: linear-gradient(135deg, #3367d6 0%, #2d8f47 100%);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

.auth-cta-btn:active {
  transform: translateY(0);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.auth-cta-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: inherit;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
}

.auth-cta-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.1);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-user-info {
    display: none;
  }

  .auth-user-dropdown {
    right: -8px;
    min-width: 200px;
  }

  .auth-sign-in-container {
    flex-direction: column;
    gap: 6px;
  }

  .auth-google-sign-in-btn,
  .auth-email-sign-in-btn {
    padding: 8px 12px;
    font-size: 13px;
  }

  .auth-cta-btn {
    padding: 10px 20px;
    font-size: 15px;
  }

  .auth-modal-content {
    margin: 10px;
    padding: 24px;
  }
}

@media (max-width: 480px) {
  .auth-google-sign-in-btn span,
  .auth-email-sign-in-btn span {
    display: none;
  }

  .auth-google-sign-in-btn,
  .auth-email-sign-in-btn {
    padding: 8px;
    min-width: 44px;
    justify-content: center;
  }

  .auth-user-dropdown {
    right: -16px;
    min-width: 180px;
  }

  .auth-cta-btn {
    padding: 10px 16px;
    font-size: 14px;
  }

  .auth-modal-content {
    margin: 5px;
    padding: 20px;
  }

  .auth-modal-header h2 {
    font-size: 20px;
  }
}
